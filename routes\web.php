<?php
include 'web2.php';
include 'web3.php';
include 'course_offering_curriculum.php';
include 'collegeadmin.php';
include 'subjects.php';
include 'superadmin.php';
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/



Auth::routes();
Route::get('/', 'HomeController@index')->name('home');

Route::group(['middleware' => 'auth'], function () {
    //    Route::get('/link1', function ()    {
//        // Uses Auth Middleware
//    });

    //Please do not remove this if you want adminlte:route and adminlte:link commands to works correctly.
    #adminlte_routes
});

//curriculum management
Route::get('/admin/curiculum_management/curriculum', 'Admin\CurriculumController@index');
Route::get('/admin/curriculum_management/view_curriculums/{program_code}', 'Admin\CurriculumController@viewcurricula');
Route::get('/admin/curriculum_management/list_curriculum/{program_code}/{curriculum_year}', 'Admin\CurriculumController@listcurriculum');
Route::get('/admin/curriculum_management/archive_curriculum', 'Admin\CurriculumController@archive_curriculum');
Route::get('/admin/curriculum_management/archived_subjects', 'Admin\CurriculumController@archived_subjects');
Route::get('/admin/curriculum_management/fix_course_names', 'Admin\CurriculumController@fixCourseNames');

//curriculum management - SuperAdmin
Route::get('/superadmin/curriculum_management/curriculum', 'SuperAdmin\CurriculumController@index');
Route::get('/superadmin/curriculum_management/view_curriculums/{program_code}', 'SuperAdmin\CurriculumController@viewcurricula');
Route::get('/superadmin/curriculum_management/list_curriculum/{program_code}/{curriculum_year}', 'SuperAdmin\CurriculumController@listcurriculum');
Route::post('/superadmin/curriculum_management/curriculum/addcourse', 'SuperAdmin\CurriculumController@add_course');
Route::post('/superadmin/curriculum_management/edit_curriculum', 'SuperAdmin\CurriculumController@edit_curriculum');
Route::post('/superadmin/curriculum_management/archive_curriculum', 'SuperAdmin\CurriculumController@archive_curriculum');
Route::get('/superadmin/curriculum_management/archived_subjects', 'SuperAdmin\CurriculumController@archived_subjects');
Route::get('/superadmin/curriculum_management/fix_course_names', 'SuperAdmin\CurriculumController@fixCourseNames');

//curriculum management - SuperAdmin
Route::get('/superadmin/curriculum_management/add_curriculum', 'SuperAdmin\AddCurriculumController@index');
Route::post('/superadmin/curriculum_management/add_curriculum/save_changes', 'SuperAdmin\AddCurriculumController@save_changes');
Route::get('/superadmin/curriculum_management/upload_curriculum', 'SuperAdmin\UploadCurriculumController@index');
Route::post('/superadmin/curriculum_management/upload', 'SuperAdmin\UploadCurriculumController@upload');
Route::post('/superadmin/curriculum_management/manual_upload', 'SuperAdmin\UploadCurriculumController@manual_upload');
Route::post('/superadmin/curriculum_management/upload/save_changes', 'SuperAdmin\UploadCurriculumController@save_changes');

//add curriculum - Admin
Route::get('/admin/curriculum_management/add_curriculum', 'Admin\AddCurriculumController@index');
Route::post('/admin/curriculum_management/add_curriculum/save_changes', 'Admin\AddCurriculumController@save_changes');
Route::get('/admin/curriculum_management/upload_curriculum', 'Admin\UploadCurriculumController@index');
Route::post('/admin/curriculum_management/upload', 'Admin\UploadCurriculumController@upload');
Route::post('/admin/curriculum_management/manual_upload', 'Admin\UploadCurriculumController@manual_upload');
Route::post('/admin/curriculum_management/upload/save_changes', 'Admin\UploadCurriculumController@save_changes');
Route::post('/admin/curriculum_management/curriculum/addcourse', 'Admin\CurriculumController@add_course');

//curriculum viewing
Route::get('/admin/curriculum_management/curriculum', 'Admin\CurriculumController@index');
Route::get('/admin/curriculum_management/view_curriculums/{program_code}', 'Admin\CurriculumController@viewcurricula');
Route::get('/admin/curriculum_management/list_curriculum/{program_code}/{curriculum_year}', 'Admin\CurriculumController@listcurriculum');
Route::post('/admin/curriculum_management/edit_curriculum', 'Admin\CurriculumController@edit_curriculum');
Route::post('/admin/curriculum_management/archive_curriculum', 'Admin\CurriculumController@archive_curriculum');
Route::get('/admin/curriculum_management/archived_subjects', 'Admin\CurriculumController@archived_subjects');

//college-curriculum integration - Admin
Route::get('/admin/curriculum_management/colleges', 'Admin\CollegeCurriculumController@index');
Route::get('/admin/curriculum_management/college/{college_code}', 'Admin\CollegeCurriculumController@viewPrograms');
Route::get('/admin/curriculum_management/college/{college_code}/curricula', 'Admin\CollegeCurriculumController@viewCurricula');
Route::get('/admin/curriculum_management/college/{college_code}/curriculum/{curriculum_year}', 'Admin\CollegeCurriculumController@viewCurriculum');
Route::post('/admin/curriculum_management/college/sync_programs', 'Admin\CollegeCurriculumController@syncPrograms');
Route::get('/ajax/admin/curriculum/filter_by_college', 'Admin\Ajax\CollegeCurriculumAjax@filterProgramsByCollege');
Route::get('/ajax/admin/curriculum/get_curriculum_years', 'Admin\Ajax\CollegeCurriculumAjax@getCurriculumYears');
Route::get('/ajax/admin/curriculum/get_college_programs', 'Admin\Ajax\CollegeCurriculumAjax@getCollegePrograms');

//college-curriculum integration - SuperAdmin
Route::get('/superadmin/curriculum_management/colleges', 'SuperAdmin\CollegeCurriculumController@index');
Route::get('/superadmin/curriculum_management/college/{college_code}', 'SuperAdmin\CollegeCurriculumController@viewPrograms');
Route::get('/superadmin/curriculum_management/college/{college_code}/curricula', 'SuperAdmin\CollegeCurriculumController@viewCurricula');
Route::get('/superadmin/curriculum_management/college/{college_code}/curriculum/{curriculum_year}', 'SuperAdmin\CollegeCurriculumController@viewCurriculum');
Route::post('/superadmin/curriculum_management/college/sync_programs', 'SuperAdmin\CollegeCurriculumController@syncPrograms');
Route::get('/ajax/superadmin/curriculum/filter_by_college', 'SuperAdmin\Ajax\CollegeCurriculumAjax@filterProgramsByCollege');
Route::get('/ajax/superadmin/curriculum/get_curriculum_years', 'SuperAdmin\Ajax\CollegeCurriculumAjax@getCurriculumYears');
Route::get('/ajax/superadmin/curriculum/get_college_programs', 'SuperAdmin\Ajax\CollegeCurriculumAjax@getCollegePrograms');


//instructor
Route::get('/admin/instructor/add_instructor', 'Users\Instructor\ViewInstructorsController@index');
Route::post('/admin/instructor/add_new_instructor', 'Users\Instructor\ViewInstructorsController@add');
Route::get('/admin/instructor/view_instructor', 'Users\Instructor\ViewInstructorsController@view_add');
Route::post('/admin/instructor/update/{id}', [
    'uses' => 'Users\Instructor\ViewInstructorsController@updateinstructor',
    'as' => 'admin.updateinstructor'
]);

Route::get('/registrar_college/curriculum_management/edit_curriculum/{curriculum_id}', 'RegistrarCollege\CurriculumManagement\CurriculumController@view_course_curriculum');
Route::post('/registrar_college/curriculum_management/edit_curriculum/update/{curriculum_id}', 'RegistrarCollege\CurriculumManagement\CurriculumController@update_course_curriculum');
Route::post('/registrar_college/curriculum_management/upload/save_changes', 'RegistrarCollege\CurriculumManagement\UploadCurriculumController@save_changes');

//reports
Route::get('/admin/instructor/instructor_reports', 'Reports\InstructorReportsController@view_add');
Route::get('/admin/instructor/view_instructor_account/{id}', 'Users\Instructor\ViewInstructorsController@view_info');

//edit faculty reports
Route::get('/registrar_college/curriculum_management/edit_faculty_loading/{idno}/{type_of_period}', 'RegistrarCollege\CurriculumManagement\FacultyLoadingController@edit_faculty_loading');
Route::get('/admin/instructor/edit_faculty_loading', 'RegistrarCollege\CurriculumManagement\FacultyLoadingController@edit_faculty_loading');
Route::get('/admin/instructor/edit_faculty_loading/{id}', 'Admin\FacultyLoadingController@instructorlist_reports');

//add new user


//ajax
Route::get('/admin/curriculum_management/ajax/edityear', 'Users\Instructor\ViewInstructorsController@edityear');
Route::get('/admin/curriculum_management/ajax/updateyear', 'Users\Instructor\ViewInstructorsController@updateyear');
Route::get('/admin/curriculum_management/ajax/refreshcurriculum', 'Users\Instructor\ViewInstructorsController@refreshcurriculum');
Route::get('/ajax/admin/curriculum/filter_programs', 'Admin\Ajax\CurriculumAjax@filterPrograms');
Route::get('/ajax/superadmin/curriculum/filter_programs', 'SuperAdmin\Ajax\CurriculumAjax@filterPrograms');
Route::get('/ajax/admin/college/get_programs', 'Admin\Ajax\CollegeAjax@get_programs');
//example
Route::get('/registrar_college/instructor/view_instructor', 'RegistrarCollege\Instructor\ViewInstructorsController@index');
Route::get('/registrar_college/instructor/add_instructor', 'RegistrarCollege\Instructor\ViewInstructorsController@view_add');
Route::post('/registrar_college/instructor/add_new_instructor', 'RegistrarCollege\Instructor\ViewInstructorsController@add');
Route::get('/registrar_college/instructor/modify_instructor/{idno}', 'RegistrarCollege\Instructor\ViewInstructorsController@view_modify');
Route::post('/registrar_college/instructor/modify_old_instructor', 'RegistrarCollege\Instructor\ViewInstructorsController@modify');
