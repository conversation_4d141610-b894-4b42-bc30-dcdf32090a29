<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 50){
        $layout = 'layouts.collegeadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>



<?php $__env->startSection('main-content'); ?>
<section class="content-header">
    <h1><i class="fa fa-users"></i>
        Faculty Management
        <small><?php echo e(isset($college) ? $college->college_name : Auth::user()->college_code); ?></small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="<?php echo e(url('/')); ?>"><i class="fa fa-home"></i> Home</a></li>
        <li class="active">Faculty Management</li>
    </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">Faculty Members</h3>
                    <div class="box-tools pull-right">
                        <a href="<?php echo e(route('collegeadmin.faculty.create')); ?>" class="btn btn-primary btn-sm">
                            <i class="fa fa-plus"></i> Add New Faculty
                        </a>
                    </div>
                </div>
                <div class="box-body">
                    <?php if(Session::has('success')): ?>
                        <div class="alert alert-success">
                            <?php echo e(Session::get('success')); ?>

                        </div>
                    <?php endif; ?>

                    <?php if(Session::has('error')): ?>
                        <div class="alert alert-danger">
                            <?php echo e(Session::get('error')); ?>

                        </div>
                    <?php endif; ?>

                    <!-- Search and Filter Section -->
                    <div class="row" style="margin-bottom: 20px;">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="department-filter">Filter by Department:</label>
                                <select id="department-filter" class="form-control">
                                    <option value="">All Departments</option>
                                    <?php
                                        $departments = $faculty->pluck('instructorInfo.department')->filter()->unique()->sort();
                                    ?>
                                    <?php $__currentLoopData = $departments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $department): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($department); ?>"><?php echo e($department); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="status-filter">Filter by Faculty Status:</label>
                                <select id="status-filter" class="form-control">
                                    <option value="">All Status</option>
                                    <?php
                                        $statuses = $faculty->pluck('instructorInfo.employee_type')->filter()->unique()->sort();
                                    ?>
                                    <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($status); ?>"><?php echo e($status); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <?php if(count($faculty) > 0): ?>
                        <div class="table-responsive">
                            <table id="faculty-table" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>ID Number</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>College</th>
                                        <th>Department</th>
                                        <th>Faculty Status</th>
                                        <th>Contact</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $faculty; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php
                                            $info = $member->instructorInfo;
                                        ?>
                                        <tr>
                                            <td><?php echo e($member->username ?? $member->id); ?></td>
                                            <td>
                                                <strong><?php echo e(strtoupper($member->lastname)); ?>, <?php echo e(strtoupper($member->name)); ?></strong>
                                                <?php if($member->middlename): ?>
                                                    <?php echo e(strtoupper($member->middlename)); ?>

                                                <?php endif; ?>
                                                <?php if($member->extensionname): ?>
                                                    <?php echo e(strtoupper($member->extensionname)); ?>

                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo e($member->email); ?></td>
                                            <td>
                                                <span class="label label-primary"><?php echo e($college->college_name ?? $member->college_code); ?></span>
                                            </td>
                                            <td><?php echo e($info ? $info->department : 'N/A'); ?></td>
                                            <td>
                                                <?php if($info && $info->employee_type): ?>
                                                    <span class="label label-success"><?php echo e($info->employee_type); ?></span>
                                                <?php else: ?>
                                                    <span class="label label-default">N/A</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if($info && $info->cell_no): ?>
                                                    <small><i class="fa fa-phone"></i> <?php echo e($info->cell_no); ?></small>
                                                <?php else: ?>
                                                    <small class="text-muted">No contact</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="<?php echo e(route('collegeadmin.faculty.show', $member->id)); ?>" class="btn btn-info btn-xs" title="View Details">
                                                        <i class="fa fa-eye"></i>
                                                    </a>
                                                    <a href="<?php echo e(route('collegeadmin.faculty.edit', $member->id)); ?>" class="btn btn-primary btn-xs" title="Edit Faculty">
                                                        <i class="fa fa-pencil"></i>
                                                    </a>
                                                    <a href="<?php echo e(route('collegeadmin.faculty_loading.generate_schedule', $member->id)); ?>" class="btn btn-success btn-xs" title="View Schedule">
                                                        <i class="fa fa-calendar"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-danger btn-xs" title="Deactivate Faculty" onclick="confirmDeactivate(<?php echo e($member->id); ?>)">
                                                        <i class="fa fa-ban"></i>
                                                    </button>
                                                </div>

                                                <!-- Hidden form for deletion -->
                                                <form id="deactivate-form-<?php echo e($member->id); ?>" action="<?php echo e(route('collegeadmin.faculty.destroy', $member->id)); ?>" method="POST" style="display: none;">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                </form>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            No faculty members found for your college.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="<?php echo e(asset('plugins/datatables/jquery.dataTables.js')); ?>"></script>
<script src="<?php echo e(asset('plugins/datatables/dataTables.bootstrap.js')); ?>"></script>
<script>
    $(function() {
        // Initialize DataTable
        var table = $('#faculty-table').DataTable({
            "paging": true,
            "lengthChange": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "pageLength": 25,
            "order": [[ 1, "asc" ]], // Sort by name column
            "columnDefs": [
                { "orderable": false, "targets": 7 } // Disable sorting on Actions column
            ]
        });

        // Department filter
        $('#department-filter').on('change', function() {
            var department = this.value;
            if (department) {
                table.column(4).search('^' + department + '$', true, false).draw();
            } else {
                table.column(4).search('').draw();
            }
        });

        // Status filter
        $('#status-filter').on('change', function() {
            var status = this.value;
            if (status) {
                table.column(5).search(status).draw();
            } else {
                table.column(5).search('').draw();
            }
        });
    });

    // Confirmation dialog for faculty deactivation
    function confirmDeactivate(facultyId) {
        if (confirm('Are you sure you want to deactivate this faculty member? This action will remove their access to the system.')) {
            document.getElementById('deactivate-form-' + facultyId).submit();
        }
    }

    // Show success/error messages with auto-hide
    $(document).ready(function() {
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    });
</script>
<?php $__env->stopPush(); ?>


<?php echo $__env->make($layout, array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>